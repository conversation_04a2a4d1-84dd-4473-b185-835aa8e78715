import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import CTA from '@/components/CTA';
import TrustBadge from '@/components/TrustBadge';
import { 
  FaStethoscope, 
  FaCalendarCheck, 
  FaLaptopMedical, 
  FaHeartbeat, 
  FaUserMd, 
  FaVials,
  FaPhone
} from 'react-icons/fa';

export const metadata: Metadata = {
  title: 'Our Services | Kirkwood Medical Practice',
  description: 'Comprehensive primary care services including family medicine, same-day appointments, telemedicine, women\'s health, chronic care management, and physical exams in Broome County, NY.',
};

export default function ServicesPage() {
  const services = [
    {
      id: 'family-medicine',
      icon: FaStethoscope,
      title: 'Family Medicine & Primary Care',
      description: 'Comprehensive healthcare for all ages with providers who understand multi-generational family health patterns and provide continuous, coordinated care.',
      features: [
        'Comprehensive health assessments',
        'Preventive care and screenings',
        'Acute illness treatment',
        'Health maintenance and wellness planning'
      ],
      color: 'medical-blue'
    },
    {
      id: 'same-day-care',
      icon: FaCalendar<PERSON>heck,
      title: 'Same-Day Sick Visits & Urgent Care',
      description: 'Priority scheduling for urgent medical needs with minimal wait times and comprehensive treatment for immediate health concerns.',
      features: [
        'Same-day appointment availability',
        'Walk-in hours for urgent needs',
        'Minor injury and illness treatment',
        'Rapid diagnostic and treatment protocols'
      ],
      color: 'care-red'
    },
    {
      id: 'telemedicine',
      icon: FaLaptopMedical,
      title: 'Telemedicine & Virtual Consultations',
      description: 'Convenient virtual healthcare visits that eliminate travel time while providing secure, effective medical care from home.',
      features: [
        'Virtual follow-up appointments',
        'Prescription management consultations',
        'Chronic condition monitoring',
        'HIPAA-compliant secure platform'
      ],
      color: 'healing-teal'
    },
    {
      id: 'physical-exams',
      icon: FaUserMd,
      title: 'Annual & School Physicals',
      description: 'Thorough health assessments for wellness maintenance, school requirements, sports participation, and employment needs.',
      features: [
        'Annual wellness examinations',
        'School and sports physicals',
        'Pre-employment health screenings',
        'Quick documentation turnaround'
      ],
      color: 'medical-blue'
    },
    {
      id: 'womens-health',
      icon: FaHeartbeat,
      title: 'Women\'s Health Services',
      description: 'Specialized healthcare designed for women\'s unique health needs throughout all life stages with compassionate, comprehensive care.',
      features: [
        'Annual gynecological examinations',
        'Family planning consultations',
        'Prenatal care coordination',
        'Menopause support and management'
      ],
      color: 'care-red'
    },
    {
      id: 'chronic-care',
      icon: FaVials,
      title: 'Chronic Condition Management',
      description: 'Expert ongoing care for chronic conditions that improves quality of life and prevents complications through systematic health management.',
      features: [
        'Diabetes monitoring and treatment',
        'Hypertension management',
        'Heart disease care coordination',
        'Medication optimization protocols'
      ],
      color: 'healing-teal'
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-medical-blue to-trust-navy text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold font-source-sans mb-6">
              Our Services
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Complete Primary Care Solutions providing comprehensive healthcare for all ages throughout Broome County, New York.
            </p>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold font-source-sans text-trust-navy mb-4">
              Comprehensive Healthcare Services
            </h2>
            <p className="text-xl text-comfort-gray max-w-3xl mx-auto">
              From same-day urgent care to ongoing chronic disease management, our services are designed to meet all your healthcare needs with personalized attention and expert medical care.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {services.map((service, index) => (
              <div key={service.id} className="bg-white rounded-lg shadow-lg overflow-hidden border-t-4 border-current" style={{borderTopColor: service.color === 'medical-blue' ? '#3351AB' : service.color === 'care-red' ? '#EC1D26' : '#4A9B9B'}}>
                <div className="p-8">
                  <div className="flex items-center mb-6">
                    <div className={`w-16 h-16 rounded-full flex items-center justify-center mr-4 bg-opacity-10 ${
                      service.color === 'medical-blue' ? 'bg-medical-blue' : 
                      service.color === 'care-red' ? 'bg-care-red' : 'bg-healing-teal'
                    }`}>
                      <service.icon className={`w-8 h-8 ${
                        service.color === 'medical-blue' ? 'text-medical-blue' : 
                        service.color === 'care-red' ? 'text-care-red' : 'text-healing-teal'
                      }`} />
                    </div>
                    <h3 className="text-2xl font-bold font-source-sans text-trust-navy">
                      {service.title}
                    </h3>
                  </div>
                  
                  <p className="text-comfort-gray mb-6 leading-relaxed">
                    {service.description}
                  </p>

                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-trust-navy mb-3">Key Features:</h4>
                    <ul className="space-y-2">
                      {service.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start">
                          <div className={`w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0 ${
                            service.color === 'medical-blue' ? 'bg-medical-blue' : 
                            service.color === 'care-red' ? 'bg-care-red' : 'bg-healing-teal'
                          }`}></div>
                          <span className="text-comfort-gray">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Link 
                    href={`/services/${service.id}`}
                    className={`inline-flex items-center font-medium transition-colors ${
                      service.color === 'medical-blue' ? 'text-medical-blue hover:text-blue-800' : 
                      service.color === 'care-red' ? 'text-care-red hover:text-red-700' : 'text-healing-teal hover:text-teal-700'
                    }`}
                  >
                    Learn More About {service.title.split(' ')[0]} {service.title.split(' ')[1]} →
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Additional Services */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-4">
              Additional Healthcare Services
            </h2>
            <p className="text-xl text-comfort-gray">
              Comprehensive care extending beyond our core services
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <FaVials className="w-8 h-8 text-medical-blue mx-auto mb-3" />
              <h3 className="font-semibold text-trust-navy mb-2">Prescription Refills & Management</h3>
              <p className="text-comfort-gray text-sm">Convenient prescription services and medication management</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <FaUserMd className="w-8 h-8 text-care-red mx-auto mb-3" />
              <h3 className="font-semibold text-trust-navy mb-2">Pre-employment Physicals</h3>
              <p className="text-comfort-gray text-sm">Occupational health screenings and fitness evaluations</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <FaHeartbeat className="w-8 h-8 text-healing-teal mx-auto mb-3" />
              <h3 className="font-semibold text-trust-navy mb-2">Nutritional Counseling</h3>
              <p className="text-comfort-gray text-sm">Lifestyle and dietary guidance for optimal health</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <FaStethoscope className="w-8 h-8 text-medical-blue mx-auto mb-3" />
              <h3 className="font-semibold text-trust-navy mb-2">Referrals & Care Coordination</h3>
              <p className="text-comfort-gray text-sm">Seamless specialist connections and care management</p>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Our Services */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-4">
              Why Choose Our Healthcare Services
            </h2>
            <p className="text-xl text-comfort-gray">
              The advantages of personalized primary care over large health systems
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <TrustBadge
              type="sameday"
              title="Same-Day Appointments Available"
              description="Priority scheduling for urgent medical needs with minimal wait times, ensuring you get care when you need it most."
            />
            
            <TrustBadge
              type="experience"
              title="Personal Provider Relationships"
              description="Continuous care with providers who know your medical history and understand your unique health needs and family patterns."
            />
            
            <TrustBadge
              type="insurance"
              title="Comprehensive Insurance Acceptance"
              description="Most major insurance plans accepted including Aetna, CI, Empire BCBS, Fidelis Care, Humana, Medicare, and Optum."
            />
            
            <TrustBadge
              type="telehealth"
              title="Advanced Technology Integration"
              description="Patient portal access, telehealth capabilities, and electronic health records for convenient, coordinated care."
            />
          </div>
        </div>
      </section>

      {/* Service Areas */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-8">
            Services Available Throughout Broome County
          </h2>
          <div className="grid md:grid-cols-3 gap-8 mb-8">
            <div>
              <h3 className="text-xl font-semibold text-trust-navy mb-2">Kirkwood</h3>
              <p className="text-comfort-gray">Complete primary care services at our home base</p>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-trust-navy mb-2">Binghamton</h3>
              <p className="text-comfort-gray">Full-service healthcare for the region's largest city</p>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-trust-navy mb-2">Conklin</h3>
              <p className="text-comfort-gray">Accessible rural healthcare with telehealth options</p>
            </div>
          </div>
          <CTA href="/locations" variant="secondary">
            View Service Area Details
          </CTA>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-medical-blue to-trust-navy text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold font-source-sans mb-6">
            Experience Comprehensive Primary Care
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            From same-day appointments to ongoing chronic care management, we provide the complete range of primary care services your family needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <CTA href="/contact" variant="primary" size="lg">
              Schedule Your Appointment
            </CTA>
            <CTA href="tel:**********" variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-medical-blue">
              <FaPhone className="mr-2" />
              Call (*************
            </CTA>
          </div>
        </div>
      </section>
    </div>
  );
}