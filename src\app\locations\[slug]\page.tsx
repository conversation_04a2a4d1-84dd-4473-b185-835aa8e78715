import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import CTA from '@/components/CTA';
import TrustBadge from '@/components/TrustBadge';
import { 
  FaMapMarkerAlt, 
  FaPhone, 
  FaStethoscope,
  FaCalendarCheck,
  FaLaptopMedical,
  FaHeartbeat,
  FaUserMd,
  FaVials,
  FaCheckCircle
} from 'react-icons/fa';

interface LocationPageProps {
  params: Promise<{
    slug: string;
  }>;
}

// Static generation of all location pages
export async function generateStaticParams() {
  return [
    { slug: 'kirkwood' },
    { slug: 'binghamton' },
    { slug: 'conklin' }
  ];
}

// Location data
const locationsData = {
  'kirkwood': {
    name: 'Kirkwood, NY',
    title: 'Kirkwood\'s Trusted Primary Care Practice',
    seoTitle: 'Kirkwood Medical Practice - Primary Care - Kirkwood, New York',
    description: 'Comprehensive family medicine for Kirkwood residents with same-day sick visits, annual physicals, chronic disease management, and telemedicine services.',
    overview: 'As Kirkwood\'s established primary care practice, we provide complete healthcare services designed specifically for our local community. Our providers understand the unique needs of Kirkwood families and deliver personalized care that builds lasting health partnerships.',
    color: 'medical-blue',
    icon: '🏠',
    services: [
      {
        title: 'Same-Day Sick Visits',
        description: 'Quick access to medical care when illness strikes, with minimal wait times and comprehensive treatment for flu, infections, minor injuries, and acute symptoms.'
      },
      {
        title: 'Annual Physical Examinations',
        description: 'Thorough wellness examinations including age-appropriate screenings, vaccination updates, and preventive care planning to maintain optimal health.'
      },
      {
        title: 'Chronic Disease Management',
        description: 'Expert ongoing care for diabetes, hypertension, heart disease, arthritis, and other chronic conditions with regular monitoring and medication optimization.'
      },
      {
        title: 'Women\'s Health Services',
        description: 'Comprehensive women\'s healthcare including annual exams, family planning, prenatal coordination, and reproductive health management.'
      },
      {
        title: 'Telemedicine Consultations',
        description: 'Convenient virtual visits for follow-up appointments, prescription refills, and health consultations from the comfort of your Kirkwood home.'
      },
      {
        title: 'Prescription Management',
        description: 'Efficient prescription refills, medication reviews, and pharmaceutical coordination with local Kirkwood pharmacies.'
      }
    ],
    serviceAreas: [
      'Downtown Kirkwood',
      'Residential neighborhoods',
      'Rural Kirkwood communities',
      'Adjacent Broome County areas'
    ],
    faqs: [
      {
        question: 'What insurance do you accept in Kirkwood?',
        answer: 'We accept most major insurance plans including Aetna, CI, Empire BCBS, Fidelis Care, Humana, Medicare, and Optum, ensuring accessible care for Kirkwood residents.'
      },
      {
        question: 'Do you offer same-day appointments?',
        answer: 'Yes, we prioritize same-day appointment availability for urgent medical needs and acute illnesses.'
      },
      {
        question: 'What services are available via telemedicine?',
        answer: 'Virtual consultations for follow-up care, prescription management, chronic condition monitoring, and many routine healthcare needs.'
      },
      {
        question: 'How do I transfer my medical records?',
        answer: 'Our staff will coordinate the secure transfer of your medical records from your previous provider to ensure continuity of care.'
      }
    ]
  },
  'binghamton': {
    name: 'Binghamton, NY',
    title: 'Binghamton\'s Comprehensive Primary Care Services',
    seoTitle: 'Kirkwood Medical Practice - Primary Care - Binghamton, New York',
    description: 'Complete healthcare solutions for Binghamton families with family medicine, urgent care, women\'s health, chronic condition support, and virtual care options.',
    overview: 'Serving Broome County\'s largest city, we bring personalized primary care to Binghamton residents who deserve an alternative to large health system care. Our practice combines the resources of modern medicine with the personal attention that only an independent practice can provide.',
    color: 'care-red',
    icon: '🏙️',
    services: [
      {
        title: 'Family Medicine for All Ages',
        description: 'Complete primary care from pediatric through geriatric patients, with providers experienced in multi-generational family healthcare needs.'
      },
      {
        title: 'Urgent Care Services',
        description: 'Immediate medical attention for non-emergency conditions including infections, minor injuries, flu symptoms, and acute illnesses.'
      },
      {
        title: 'Women\'s Health & Obstetrics',
        description: 'Specialized women\'s healthcare including reproductive health, family planning, prenatal care coordination, and comprehensive gynecological services.'
      },
      {
        title: 'Chronic Condition Support',
        description: 'Expert management of diabetes, hypertension, heart disease, and other chronic conditions with regular monitoring and lifestyle counseling.'
      },
      {
        title: 'School & Sports Physicals',
        description: 'Required physical examinations for school enrollment and athletic participation, with quick documentation turnaround for busy Binghamton families.'
      },
      {
        title: 'Care Coordination Services',
        description: 'Seamless referrals to specialists and coordination with Binghamton area healthcare providers for comprehensive care management.'
      }
    ],
    serviceAreas: [
      'West Side Binghamton',
      'East Side residential areas', 
      'Johnson City border communities',
      'University area near Binghamton University'
    ],
    faqs: [
      {
        question: 'How quickly can I be seen for urgent needs?',
        answer: 'We offer same-day appointments for urgent medical conditions and work to minimize wait times for all patients.'
      },
      {
        question: 'Do you coordinate care with UHS and Guthrie systems?',
        answer: 'Yes, we maintain professional relationships with area specialists and hospitals to ensure seamless care coordination when needed.'
      },
      {
        question: 'What preventive services do you offer?',
        answer: 'Comprehensive preventive care including annual physicals, health screenings, vaccinations, and wellness planning.'
      },
      {
        question: 'Are you accepting new patients from Binghamton?',
        answer: 'Yes, we welcome new patients from throughout the Binghamton area and surrounding communities.'
      }
    ]
  },
  'conklin': {
    name: 'Conklin, NY',
    title: 'Conklin\'s Accessible Primary Care Provider',
    seoTitle: 'Kirkwood Medical Practice - Primary Care - Conklin, New York',
    description: 'Quality healthcare close to home for Conklin residents with comprehensive family care, same-day appointments, chronic disease monitoring, and telehealth accessibility.',
    overview: 'Understanding that Conklin residents face unique challenges accessing quality healthcare, we provide comprehensive primary care services designed to meet rural community needs. Our practice bridges the gap between big city medical resources and small-town personal care.',
    color: 'healing-teal',
    icon: '🌾',
    services: [
      {
        title: 'Comprehensive Family Care',
        description: 'Complete primary care services for all family members, from infants to seniors, with providers who understand rural healthcare needs and lifestyle factors.'
      },
      {
        title: 'Same-Day Appointments',
        description: 'Priority scheduling for urgent medical needs, ensuring Conklin residents don\'t have to travel far for immediate healthcare access.'
      },
      {
        title: 'Chronic Disease Monitoring',
        description: 'Expert ongoing management of diabetes, hypertension, arthritis, and other chronic conditions with flexible scheduling and telehealth options.'
      },
      {
        title: 'Preventive Health Services',
        description: 'Comprehensive wellness examinations, health screenings, vaccinations, and preventive care planning to maintain optimal health.'
      },
      {
        title: 'Telehealth Accessibility',
        description: 'Virtual healthcare visits that eliminate travel time and provide convenient access to medical care from home.'
      },
      {
        title: 'Referral Coordination',
        description: 'Seamless connections to specialists and healthcare services throughout Broome County when advanced care is needed.'
      }
    ],
    serviceAreas: [
      'Central Conklin',
      'Rural Conklin communities',
      'Conklin Lakes area',
      'Susquehanna River corridor'
    ],
    faqs: [
      {
        question: 'What makes your practice different from larger health systems?',
        answer: 'We provide personalized attention, shorter wait times, and flexible scheduling that respects the unique needs of rural communities.'
      },
      {
        question: 'How do I schedule appointments from Conklin?',
        answer: 'Easy scheduling by phone with accommodating staff who understand rural scheduling challenges and transportation considerations.'
      },
      {
        question: 'Do you offer transportation assistance?',
        answer: 'While we don\'t provide transportation, we offer extensive telehealth services to minimize travel requirements for routine care.'
      },
      {
        question: 'What services require in-office visits vs. telehealth?',
        answer: 'Physical examinations, laboratory work, and acute care require office visits, while follow-ups, prescription management, and chronic care monitoring often work well via telehealth.'
      }
    ]
  }
};

export async function generateMetadata({ params }: LocationPageProps): Promise<Metadata> {
  const { slug } = await params;
  const location = locationsData[slug as keyof typeof locationsData];
  
  if (!location) {
    return {
      title: 'Location Not Found | Kirkwood Medical Practice'
    };
  }

  return {
    title: location.seoTitle,
    description: location.description,
  };
}

export default async function LocationDetailPage({ params }: LocationPageProps) {
  const { slug } = await params;
  const location = locationsData[slug as keyof typeof locationsData];

  if (!location) {
    notFound();
  }

  const colorClasses = {
    'medical-blue': {
      bg: 'bg-medical-blue',
      text: 'text-medical-blue',
      border: 'border-medical-blue',
      bgOpacity: 'bg-medical-blue bg-opacity-10'
    },
    'care-red': {
      bg: 'bg-care-red',
      text: 'text-care-red', 
      border: 'border-care-red',
      bgOpacity: 'bg-care-red bg-opacity-10'
    },
    'healing-teal': {
      bg: 'bg-healing-teal',
      text: 'text-healing-teal',
      border: 'border-healing-teal',
      bgOpacity: 'bg-healing-teal bg-opacity-10'
    }
  };

  const colors = colorClasses[location.color as keyof typeof colorClasses];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-medical-blue to-trust-navy text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            <div className="text-6xl mb-6">{location.icon}</div>
            <h1 className="text-4xl md:text-5xl font-bold font-source-sans mb-6">
              {location.title}
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              {location.overview}
            </p>
          </div>
        </div>
      </section>

      {/* Services Available */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-4">
              Healthcare Services in {location.name}
            </h2>
            <p className="text-xl text-comfort-gray max-w-3xl mx-auto">
              Comprehensive primary care services designed to meet the unique needs of {location.name} residents
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {location.services.map((service, index) => (
              <div key={index} className={`bg-white p-6 rounded-lg shadow-lg border-l-4 ${colors.border}`}>
                <h3 className="text-xl font-semibold text-trust-navy mb-3">
                  {service.title}
                </h3>
                <p className="text-comfort-gray leading-relaxed">
                  {service.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Service Areas */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-4">
              Local Areas We Serve
            </h2>
            <p className="text-xl text-comfort-gray">
              Comprehensive healthcare throughout the {location.name} community
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {location.serviceAreas.map((area, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-md text-center">
                <FaMapMarkerAlt className={`w-6 h-6 ${colors.text} mx-auto mb-3`} />
                <h3 className="font-semibold text-trust-navy text-sm">
                  {area}
                </h3>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Trust Badges */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-4">
              Why {location.name} Residents Choose Us
            </h2>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            <TrustBadge
              type="sameday"
              title="Same-Day Appointments"
              description="Priority scheduling for urgent medical needs with convenient access from anywhere in the community."
            />
            
            <TrustBadge
              type="community"
              title="Local Community Focus"
              description={`Providers who understand the unique healthcare needs and challenges of ${location.name} residents.`}
            />
            
            <TrustBadge
              type="telehealth"
              title="Flexible Care Options"
              description="In-office visits and telemedicine options to meet your scheduling and transportation needs."
            />
            
            <TrustBadge
              type="insurance"
              title="Insurance Accepted"
              description="Comprehensive insurance acceptance to ensure affordable, accessible healthcare for all residents."
            />
          </div>
        </div>
      </section>

      {/* FAQs */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold font-source-sans text-trust-navy text-center mb-12">
            Common Questions from {location.name} Patients
          </h2>
          <div className="space-y-8">
            {location.faqs.map((faq, index) => (
              <div key={index} className={`border-l-4 ${colors.border} pl-6 py-4 bg-white`}>
                <h3 className="text-xl font-semibold text-trust-navy mb-3">
                  {faq.question}
                </h3>
                <p className="text-comfort-gray leading-relaxed">
                  {faq.answer}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Office Information */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-4">
                Visit Our Office
              </h2>
              <p className="text-xl text-comfort-gray">
                Convenient healthcare access for all {location.name} residents
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8 text-center">
              <div>
                <FaMapMarkerAlt className={`w-8 h-8 ${colors.text} mx-auto mb-3`} />
                <h3 className="text-lg font-semibold text-trust-navy mb-2">Address</h3>
                <p className="text-comfort-gray">
                  4290 Vestal Road<br />
                  Vestal, NY 13850
                </p>
              </div>

              <div>
                <FaPhone className={`w-8 h-8 ${colors.text} mx-auto mb-3`} />
                <h3 className="text-lg font-semibold text-trust-navy mb-2">Phone</h3>
                <a href="tel:**********" className={`${colors.text} hover:opacity-80 font-medium`}>
                  (*************
                </a>
              </div>

              <div>
                <FaCalendarCheck className={`w-8 h-8 ${colors.text} mx-auto mb-3`} />
                <h3 className="text-lg font-semibold text-trust-navy mb-2">Hours</h3>
                <p className="text-comfort-gray">
                  Mon-Fri: 8:00 AM - 5:00 PM<br />
                  <span className="text-sm">Walk-ins: 8-10 AM & 2-4 PM</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Related Locations */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold font-source-sans text-trust-navy text-center mb-12">
            We Also Serve
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            {Object.entries(locationsData)
              .filter(([locationSlug]) => locationSlug !== slug)
              .map(([locationSlug, loc]) => (
                <div key={locationSlug} className="bg-white p-6 rounded-lg shadow-md">
                  <div className="flex items-center mb-4">
                    <span className="text-3xl mr-4">{loc.icon}</span>
                    <h3 className="text-xl font-semibold text-trust-navy">{loc.name}</h3>
                  </div>
                  <p className="text-comfort-gray mb-4">{loc.description}</p>
                  <Link href={`/locations/${locationSlug}`} className={`${colors.text} hover:opacity-80 font-medium`}>
                    Learn More About {loc.name} →
                  </Link>
                </div>
              ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-medical-blue to-trust-navy text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold font-source-sans mb-6">
            Experience Personalized Care in {location.name}
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Join the growing number of {location.name} families who have chosen personalized primary care over large health system medicine.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <CTA href="/contact" variant="primary" size="lg">
              Schedule Your Appointment
            </CTA>
            <CTA href="tel:**********" variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-medical-blue">
              <FaPhone className="mr-2" />
              Call (*************
            </CTA>
          </div>
        </div>
      </section>
    </div>
  );
}