import Link from 'next/link';
import CTA from '@/components/CTA';
import { FaHome, FaPhone, FaStethoscope } from 'react-icons/fa';

export default function NotFound() {
  return (
    <div className="bg-white min-h-screen flex items-center justify-center">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="mb-8">
          <FaStethoscope className="w-24 h-24 text-medical-blue mx-auto mb-6 opacity-50" />
          <h1 className="text-6xl font-bold font-source-sans text-trust-navy mb-4">404</h1>
          <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-6">
            Page Not Found
          </h2>
          <p className="text-xl text-comfort-gray mb-8">
            The page you're looking for doesn't exist. It may have been moved, deleted, or you may have entered the wrong URL.
          </p>
        </div>

        <div className="space-y-4 mb-12">
          <p className="text-lg text-comfort-gray">Here are some helpful links:</p>
          <div className="grid md:grid-cols-2 gap-4">
            <Link href="/" className="flex items-center justify-center bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors">
              <FaHome className="w-5 h-5 text-medical-blue mr-3" />
              <span className="text-trust-navy font-medium">Return Home</span>
            </Link>
            <Link href="/services" className="flex items-center justify-center bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors">
              <FaStethoscope className="w-5 h-5 text-medical-blue mr-3" />
              <span className="text-trust-navy font-medium">Our Services</span>
            </Link>
            <Link href="/about" className="flex items-center justify-center bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors">
              <FaStethoscope className="w-5 h-5 text-medical-blue mr-3" />
              <span className="text-trust-navy font-medium">About Us</span>
            </Link>
            <Link href="/contact" className="flex items-center justify-center bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors">
              <FaPhone className="w-5 h-5 text-medical-blue mr-3" />
              <span className="text-trust-navy font-medium">Contact Us</span>
            </Link>
          </div>
        </div>

        <div className="border-t border-gray-200 pt-8">
          <p className="text-lg text-comfort-gray mb-6">
            Need immediate medical attention or have questions about our services?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <CTA href="/contact" variant="primary">
              Schedule Appointment
            </CTA>
            <CTA href="tel:**********" variant="outline">
              <FaPhone className="mr-2" />
              Call (*************
            </CTA>
          </div>
        </div>
      </div>
    </div>
  );
}