'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FaBars, FaTimes, FaPhone } from 'react-icons/fa';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'About Us', href: '/about' },
    { name: 'Services', href: '/services' },
    { name: 'Locations', href: '/locations' },
    { name: 'Contact', href: '/contact' },
  ];

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center">
              <Image
                src="/images/kirkwood-medical-practice-logo.png"
                alt="Kirkwood Medical Practice Logo"
                width={200}
                height={60}
                className="h-12 w-auto"
                priority
              />
              <span className="ml-3 text-xl font-bold font-source-sans text-medical-blue hidden sm:block">
                Kirkwood Medical Practice
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8" role="navigation" aria-label="Main navigation">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-trust-navy hover:text-medical-blue focus:text-medical-blue px-3 py-2 text-sm font-medium transition-colors duration-200 border-b-2 border-transparent hover:border-medical-blue focus:border-medical-blue focus:outline-none focus:ring-2 focus:ring-medical-blue focus:ring-offset-2 rounded-sm"
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Phone Number */}
          <div className="hidden md:flex items-center">
            <a
              href="tel:**********"
              className="flex items-center bg-care-red text-white px-4 py-2 rounded-lg hover:bg-red-700 focus:bg-red-700 focus:outline-none focus:ring-2 focus:ring-care-red focus:ring-offset-2 transition-colors duration-200 font-medium"
              aria-label="Call Kirkwood Medical Practice at (*************"
            >
              <FaPhone className="mr-2" aria-hidden="true" />
              (*************
            </a>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="text-trust-navy hover:text-medical-blue focus:text-medical-blue p-2 focus:outline-none focus:ring-2 focus:ring-medical-blue focus:ring-offset-2 rounded"
              aria-label={isMenuOpen ? "Close menu" : "Open menu"}
              aria-expanded={isMenuOpen}
              aria-controls="mobile-menu"
            >
              {isMenuOpen ? <FaTimes size={24} aria-hidden="true" /> : <FaBars size={24} aria-hidden="true" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div id="mobile-menu" className="md:hidden bg-white border-t border-gray-200">
            <nav className="px-2 pt-2 pb-3 space-y-1" role="navigation" aria-label="Mobile navigation">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => setIsMenuOpen(false)}
                  className="text-trust-navy hover:text-medical-blue focus:text-medical-blue hover:bg-gray-50 focus:bg-gray-50 block px-3 py-2 text-base font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-medical-blue focus:ring-inset rounded"
                >
                  {item.name}
                </Link>
              ))}
              <a
                href="tel:**********"
                className="flex items-center text-care-red hover:text-red-700 focus:text-red-700 px-3 py-2 text-base font-medium focus:outline-none focus:ring-2 focus:ring-care-red focus:ring-inset rounded"
                aria-label="Call Kirkwood Medical Practice at (*************"
              >
                <FaPhone className="mr-2" aria-hidden="true" />
                (*************
              </a>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}