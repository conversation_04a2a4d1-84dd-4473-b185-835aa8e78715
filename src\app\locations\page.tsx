import { Metadata } from 'next';
import Link from 'next/link';
import CTA from '@/components/CTA';
import TrustBadge from '@/components/TrustBadge';
import { FaMapMarkerAlt, FaPhone, FaCar, FaRoute } from 'react-icons/fa';

export const metadata: Metadata = {
  title: 'Service Areas | Kirkwood Medical Practice',
  description: 'Kirkwood Medical Practice serves Kirkwood, Binghamton, Conklin, and throughout Broome County, NY with comprehensive primary care services and convenient access.',
};

export default function LocationsPage() {
  const locations = [
    {
      id: 'kirkwood',
      name: 'Kirkwood, NY',
      title: 'Primary Care in Kirkwood',
      description: 'Our home base serving the local community with comprehensive family healthcare and personalized medical attention.',
      features: [
        'Complete primary care services',
        'Same-day appointment priority',
        'All providers available',
        'Local community focus'
      ],
      neighborhoods: [
        'Downtown Kirkwood',
        'Residential neighborhoods',
        'Rural Kirkwood communities',
        'Adjacent Broome County areas'
      ],
      color: 'medical-blue',
      icon: '🏠'
    },
    {
      id: 'binghamton',
      name: 'Binghamton, NY', 
      title: 'Family Medicine in Binghamton',
      description: 'Comprehensive care for the region\'s largest city with providers who understand urban healthcare needs and busy family schedules.',
      features: [
        'Multi-generational family care',
        'Urban healthcare expertise',
        'Flexible scheduling options',
        'University area services'
      ],
      neighborhoods: [
        'West Side Binghamton',
        'East Side residential areas',
        'Johnson City border communities',
        'University area near Binghamton University'
      ],
      color: 'care-red',
      icon: '🏙️'
    },
    {
      id: 'conklin',
      name: 'Conklin, NY',
      title: 'Medical Services in Conklin', 
      description: 'Accessible healthcare for rural communities with providers who understand unique rural health challenges and transportation considerations.',
      features: [
        'Rural healthcare specialization',
        'Telehealth accessibility',
        'Flexible scheduling',
        'Transportation considerations'
      ],
      neighborhoods: [
        'Central Conklin',
        'Rural Conklin communities',
        'Conklin Lakes area',
        'Susquehanna River corridor'
      ],
      color: 'healing-teal',
      icon: '🌾'
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-medical-blue to-trust-navy text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold font-source-sans mb-6">
              Service Areas
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Convenient Primary Care Throughout Broome County - serving Kirkwood, Binghamton, Conklin, and surrounding communities with comprehensive healthcare services.
            </p>
          </div>
        </div>
      </section>

      {/* Office Location */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="lg:flex">
              <div className="lg:w-1/2 p-8">
                <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-6">
                  Our Office Location
                </h2>
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <FaMapMarkerAlt className="w-6 h-6 text-medical-blue mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="text-lg font-semibold text-trust-navy">Address</h3>
                      <p className="text-comfort-gray">
                        4290 Vestal Road<br />
                        Vestal, NY 13850
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <FaPhone className="w-6 h-6 text-care-red mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="text-lg font-semibold text-trust-navy">Phone</h3>
                      <a href="tel:**********" className="text-care-red hover:text-red-700 font-medium">
                        (*************
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <FaCar className="w-6 h-6 text-healing-teal mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="text-lg font-semibold text-trust-navy">Accessibility</h3>
                      <p className="text-comfort-gray">
                        Convenient parking available<br />
                        Wheelchair accessible<br />
                        Public transportation nearby
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-8">
                  <CTA href="/contact" variant="primary" size="lg">
                    Schedule Your Visit
                  </CTA>
                </div>
              </div>
              
              <div className="lg:w-1/2 bg-gray-100 flex items-center justify-center p-8">
                <div className="text-center text-gray-500">
                  <FaMapMarkerAlt className="w-24 h-24 mx-auto mb-4 opacity-50" />
                  <p className="text-lg">Interactive Map Coming Soon</p>
                  <p className="text-sm">Located in the heart of Broome County</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Service Areas */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold font-source-sans text-trust-navy mb-4">
              Communities We Serve
            </h2>
            <p className="text-xl text-comfort-gray max-w-3xl mx-auto">
              From urban Binghamton to rural Conklin, we provide personalized primary care that meets the unique needs of each community we serve.
            </p>
          </div>

          <div className="space-y-12">
            {locations.map((location, index) => (
              <div key={location.id} className={`bg-white rounded-lg shadow-lg overflow-hidden ${index % 2 === 1 ? 'lg:flex-row-reverse' : ''} lg:flex`}>
                <div className="lg:w-1/2 p-8">
                  <div className="flex items-center mb-6">
                    <span className="text-4xl mr-4">{location.icon}</span>
                    <div>
                      <h3 className="text-3xl font-bold font-source-sans text-trust-navy">
                        {location.title}
                      </h3>
                      <p className={`text-lg font-medium ${
                        location.color === 'medical-blue' ? 'text-medical-blue' : 
                        location.color === 'care-red' ? 'text-care-red' : 'text-healing-teal'
                      }`}>
                        {location.name}
                      </p>
                    </div>
                  </div>

                  <p className="text-comfort-gray mb-6 leading-relaxed">
                    {location.description}
                  </p>

                  <div className="grid md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <h4 className="text-lg font-semibold text-trust-navy mb-3">Key Features:</h4>
                      <ul className="space-y-2">
                        {location.features.map((feature, idx) => (
                          <li key={idx} className="flex items-start">
                            <div className={`w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0 ${
                              location.color === 'medical-blue' ? 'bg-medical-blue' : 
                              location.color === 'care-red' ? 'bg-care-red' : 'bg-healing-teal'
                            }`}></div>
                            <span className="text-comfort-gray text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="text-lg font-semibold text-trust-navy mb-3">Areas Served:</h4>
                      <ul className="space-y-2">
                        {location.neighborhoods.map((neighborhood, idx) => (
                          <li key={idx} className="flex items-start">
                            <FaRoute className={`w-3 h-3 mt-2 mr-3 flex-shrink-0 ${
                              location.color === 'medical-blue' ? 'text-medical-blue' : 
                              location.color === 'care-red' ? 'text-care-red' : 'text-healing-teal'
                            }`} />
                            <span className="text-comfort-gray text-sm">{neighborhood}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <Link 
                    href={`/locations/${location.id}`}
                    className={`inline-flex items-center font-medium transition-colors ${
                      location.color === 'medical-blue' ? 'text-medical-blue hover:text-blue-800' : 
                      location.color === 'care-red' ? 'text-care-red hover:text-red-700' : 'text-healing-teal hover:text-teal-700'
                    }`}
                  >
                    Learn More About {location.name} Services →
                  </Link>
                </div>

                <div className={`lg:w-1/2 p-8 ${
                  location.color === 'medical-blue' ? 'bg-gradient-to-br from-medical-blue to-blue-800' : 
                  location.color === 'care-red' ? 'bg-gradient-to-br from-care-red to-red-700' : 'bg-gradient-to-br from-healing-teal to-teal-700'
                } text-white flex items-center justify-center`}>
                  <div className="text-center">
                    <span className="text-8xl mb-4 block opacity-80">{location.icon}</span>
                    <h4 className="text-2xl font-bold font-source-sans mb-2">{location.name}</h4>
                    <p className="text-lg opacity-90">Healthcare Services</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Service Benefits by Location */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-4">
              Tailored Care for Each Community
            </h2>
            <p className="text-xl text-comfort-gray">
              Our approach adapts to the unique needs of urban, suburban, and rural communities
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <TrustBadge
              type="community"
              title="Local Community Understanding"
              description="Providers with deep knowledge of each community's specific health challenges, resources, and cultural considerations."
            />
            
            <TrustBadge
              type="sameday"
              title="Accessible Scheduling"
              description="Flexible appointment scheduling that accommodates urban professionals, suburban families, and rural residents' unique needs."
            />
            
            <TrustBadge
              type="telehealth"
              title="Geographic Accessibility"
              description="Telemedicine options ensure rural communities have the same access to quality healthcare as urban centers."
            />
          </div>
        </div>
      </section>

      {/* Transportation and Access */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-4">
              Easy Access from Anywhere in Broome County
            </h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <FaCar className="w-8 h-8 text-medical-blue mx-auto mb-3" />
              <h3 className="font-semibold text-trust-navy mb-2">Convenient Parking</h3>
              <p className="text-comfort-gray text-sm">Ample free parking available for all patients and visitors</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <FaRoute className="w-8 h-8 text-care-red mx-auto mb-3" />
              <h3 className="font-semibold text-trust-navy mb-2">Major Route Access</h3>
              <p className="text-comfort-gray text-sm">Easy access from Route 17, Route 11, and major county roads</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <FaMapMarkerAlt className="w-8 h-8 text-healing-teal mx-auto mb-3" />
              <h3 className="font-semibold text-trust-navy mb-2">Central Location</h3>
              <p className="text-comfort-gray text-sm">Centrally located to serve all Broome County communities equally</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <FaPhone className="w-8 h-8 text-medical-blue mx-auto mb-3" />
              <h3 className="font-semibold text-trust-navy mb-2">Telehealth Options</h3>
              <p className="text-comfort-gray text-sm">Virtual visits available for convenient remote healthcare</p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-medical-blue to-trust-navy text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold font-source-sans mb-6">
            Your Neighborhood Healthcare Provider
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Whether you're in urban Binghamton, suburban Kirkwood, or rural Conklin, we're here to provide the personalized primary care your community deserves.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <CTA href="/contact" variant="primary" size="lg">
              Schedule Your Appointment
            </CTA>
            <CTA href="tel:**********" variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-medical-blue">
              <FaPhone className="mr-2" />
              Call (*************
            </CTA>
          </div>
        </div>
      </section>
    </div>
  );
}