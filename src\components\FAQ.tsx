'use client';

import { useState } from 'react';
import { FaChevronDown, FaChevronUp } from 'react-icons/fa';

interface FAQItem {
  question: string;
  answer: string;
}

interface FAQProps {
  items: FAQItem[];
  title?: string;
  className?: string;
}

export default function FAQ({ items, title = 'Frequently Asked Questions', className = '' }: FAQProps) {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {title && (
        <h2 className="text-3xl font-bold font-source-sans text-trust-navy text-center mb-8">
          {title}
        </h2>
      )}
      
      <div className="space-y-4">
        {items.map((item, index) => (
          <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden">
            <button
              onClick={() => toggleItem(index)}
              className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
              aria-expanded={openItems.includes(index)}
            >
              <h3 className="text-lg font-semibold text-trust-navy pr-4">
                {item.question}
              </h3>
              <div className="flex-shrink-0">
                {openItems.includes(index) ? (
                  <FaChevronUp className="w-5 h-5 text-medical-blue" />
                ) : (
                  <FaChevronDown className="w-5 h-5 text-medical-blue" />
                )}
              </div>
            </button>
            
            {openItems.includes(index) && (
              <div className="px-6 pb-4">
                <div className="border-t border-gray-200 pt-4">
                  <p className="text-comfort-gray leading-relaxed">
                    {item.answer}
                  </p>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}