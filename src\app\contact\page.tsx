import { Metadata } from 'next';
import Image from 'next/image';
import ContactForm from '@/components/ContactForm';
import TrustBadge from '@/components/TrustBadge';
import CTA from '@/components/CTA';
import { FaMapMarkerAlt, FaPhone, FaEnvelope, FaClock, FaCalendarCheck } from 'react-icons/fa';

export const metadata: Metadata = {
  title: 'Contact Us | Kirkwood Medical Practice',
  description: 'Contact Kirkwood Medical Practice for appointments, questions, or general inquiries. Same-day appointments available. Serving Kirkwood, Binghamton, and Conklin.',
};

export default function ContactPage() {
  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-medical-blue to-trust-navy text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold font-source-sans mb-6">
              Contact Us
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Ready to experience personalized primary care? We're here to help you schedule your appointment or answer any questions about our services.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Information and Form */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div className="space-y-8">
              <div>
                <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-6">
                  Get in Touch
                </h2>
                <p className="text-lg text-comfort-gray mb-8">
                  Contact our friendly staff to begin your healthcare journey with Kirkwood Medical Practice. We welcome new patients and offer flexible scheduling including same-day appointments for urgent needs.
                </p>
              </div>

              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-medical-blue bg-opacity-10 rounded-full flex items-center justify-center">
                    <FaMapMarkerAlt className="w-6 h-6 text-medical-blue" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-trust-navy mb-1">Office Location</h3>
                    <p className="text-comfort-gray">
                      4290 Vestal Road<br />
                      Vestal, NY 13850
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-care-red bg-opacity-10 rounded-full flex items-center justify-center">
                    <FaPhone className="w-6 h-6 text-care-red" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-trust-navy mb-1">Phone Number</h3>
                    <a href="tel:**********" className="text-care-red hover:text-red-700 font-medium">
                      (*************
                    </a>
                    <p className="text-comfort-gray text-sm mt-1">
                      Call for appointments and urgent care needs
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-healing-teal bg-opacity-10 rounded-full flex items-center justify-center">
                    <FaEnvelope className="w-6 h-6 text-healing-teal" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-trust-navy mb-1">Email</h3>
                    <a href="mailto:<EMAIL>" className="text-healing-teal hover:text-teal-700 font-medium">
                      <EMAIL>
                    </a>
                    <p className="text-comfort-gray text-sm mt-1">
                      General inquiries and non-urgent questions
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-medical-blue bg-opacity-10 rounded-full flex items-center justify-center">
                    <FaClock className="w-6 h-6 text-medical-blue" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-trust-navy mb-1">Office Hours</h3>
                    <div className="text-comfort-gray space-y-1">
                      <p>Monday - Friday: 8:00 AM - 5:00 PM</p>
                      <p className="text-sm text-care-red font-medium">
                        Walk-in hours: 8:00-10:00 AM & 2:00-4:00 PM
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="flex items-center mb-3">
                  <FaCalendarCheck className="w-6 h-6 text-green-600 mr-3" />
                  <h3 className="text-lg font-semibold text-green-800">Same-Day Appointments</h3>
                </div>
                <p className="text-green-700">
                  Need to be seen today? We reserve time slots each day for urgent appointments. 
                  Call us first thing in the morning for the best availability.
                </p>
              </div>

              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-trust-navy">Online Services</h3>
                <div className="flex flex-col space-y-3">
                  <CTA 
                    href="https://kirkwoodmedicalpatientportal.com" 
                    external={true}
                    variant="outline"
                    className="w-full sm:w-auto"
                  >
                    Patient Portal Access
                  </CTA>
                  <CTA 
                    href="https://kirkwoodmedicalscheduling.com" 
                    external={true}
                    variant="secondary"
                    className="w-full sm:w-auto"
                  >
                    Schedule Online
                  </CTA>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div className="bg-gray-50 p-8 rounded-lg">
              <h2 className="text-2xl font-bold font-source-sans text-trust-navy mb-6">
                Send Us a Message
              </h2>
              <ContactForm />
            </div>
          </div>
        </div>
      </section>

      {/* Office Environment Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="relative">
              <Image
                src="/images/kirkwood-medical-practice-waiting-area.jpg"
                alt="Kirkwood Medical Practice comfortable waiting area"
                width={600}
                height={400}
                className="rounded-lg shadow-xl"
              />
            </div>
            <div>
              <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-6">
                Comfortable & Welcoming Environment
              </h2>
              <div className="space-y-4 text-comfort-gray">
                <p className="text-lg leading-relaxed">
                  Our modern facility is designed with patient comfort in mind. From our welcoming waiting area to our state-of-the-art examination rooms, every detail has been carefully planned to create a calming, professional healthcare environment.
                </p>
                <p className="text-lg leading-relaxed">
                  We believe that feeling comfortable and at ease is an important part of your healthcare experience. Our friendly staff and inviting atmosphere help make your visit as pleasant as possible.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Badges */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-4">
              Why Patients Choose Us
            </h2>
          </div>
          <div className="grid md:grid-cols-2 gap-8">
            <TrustBadge
              type="sameday"
              title="Same-Day Availability"
              description="Priority scheduling for urgent medical needs with minimal wait times."
            />
            <TrustBadge
              type="telehealth"
              title="Telemedicine Options"
              description="Convenient virtual appointments for follow-up care and consultations."
            />
            <TrustBadge
              type="insurance"
              title="Insurance Accepted"
              description="Most major insurance plans accepted including Medicare, Aetna, and Empire BCBS."
            />
            <TrustBadge
              type="community"
              title="Local Community Care"
              description="Providers with deep roots in Broome County who understand local health needs."
            />
          </div>
        </div>
      </section>

      {/* Service Areas */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-8">
            Serving Broome County Communities
          </h2>
          <div className="grid md:grid-cols-3 gap-8 mb-8">
            <div className="text-center">
              <FaMapMarkerAlt className="w-8 h-8 text-medical-blue mx-auto mb-3" />
              <h3 className="text-xl font-semibold text-trust-navy mb-2">Kirkwood</h3>
              <p className="text-comfort-gray">Our home base in the heart of the community</p>
            </div>
            <div className="text-center">
              <FaMapMarkerAlt className="w-8 h-8 text-care-red mx-auto mb-3" />
              <h3 className="text-xl font-semibold text-trust-navy mb-2">Binghamton</h3>
              <p className="text-comfort-gray">Serving the region's largest population center</p>
            </div>
            <div className="text-center">
              <FaMapMarkerAlt className="w-8 h-8 text-healing-teal mx-auto mb-3" />
              <h3 className="text-xl font-semibold text-trust-navy mb-2">Conklin</h3>
              <p className="text-comfort-gray">Accessible care for rural communities</p>
            </div>
          </div>
          <CTA href="/locations" variant="secondary">
            View All Service Areas
          </CTA>
        </div>
      </section>
    </div>
  );
}