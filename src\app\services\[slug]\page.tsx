import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import CTA from '@/components/CTA';
import TrustBadge from '@/components/TrustBadge';
import { 
  FaStethoscope, 
  FaCalendarCheck, 
  FaLaptopMedical, 
  FaHeartbeat, 
  FaUserMd, 
  FaVials,
  FaPhone,
  FaCheckCircle,
  FaMapMarkerAlt
} from 'react-icons/fa';

interface ServicePageProps {
  params: Promise<{
    slug: string;
  }>;
}

// Static generation of all service pages
export async function generateStaticParams() {
  return [
    { slug: 'family-medicine' },
    { slug: 'same-day-care' },
    { slug: 'telemedicine' },
    { slug: 'womens-health' },
    { slug: 'chronic-care' },
    { slug: 'physical-exams' }
  ];
}

// Service data
const servicesData = {
  'family-medicine': {
    title: 'Family Medicine & Primary Care',
    seoTitle: 'Family Medicine | Kirkwood Medical Practice',
    description: 'Comprehensive healthcare for all ages with providers who understand multi-generational family health patterns.',
    icon: FaStethoscope,
    color: 'medical-blue',
    content: {
      overview: 'Comprehensive healthcare for all ages with providers who understand multi-generational family health patterns and provide continuous, coordinated care throughout your family\'s healthcare journey.',
      services: [
        'Complete health assessments and physical examinations',
        'Preventive care and routine health screenings',
        'Acute illness diagnosis and treatment',
        'Health maintenance and wellness planning',
        'Family health history management',
        'Pediatric through geriatric care'
      ],
      process: [
        'Comprehensive health history review',
        'Complete physical examination',
        'Age-appropriate screening recommendations',
        'Personalized care plan development',
        'Family health pattern analysis',
        'Ongoing wellness monitoring'
      ],
      relatedServices: ['Annual & School Physicals', 'Chronic Condition Management', 'Women\'s Health Services'],
      faqs: [
        {
          question: 'What does comprehensive family medicine include?',
          answer: 'Our family medicine services cover complete healthcare for all family members, from infants to seniors, including preventive care, acute illness treatment, chronic disease management, and health maintenance.'
        },
        {
          question: 'How do you coordinate care for multiple family members?',
          answer: 'We maintain comprehensive family health records that help us understand genetic patterns, shared health risks, and family dynamics that affect healthcare decisions and treatment plans.'
        }
      ]
    }
  },
  'same-day-care': {
    title: 'Same-Day Sick Visits & Urgent Care',
    seoTitle: 'Same-Day Sick Visits | Kirkwood Medical Practice',
    description: 'Priority scheduling for urgent medical needs with minimal wait times and comprehensive treatment.',
    icon: FaCalendarCheck,
    color: 'care-red',
    content: {
      overview: 'When illness strikes unexpectedly, Kirkwood Medical Practice provides immediate medical attention without the long waits and impersonal care of urgent care centers. Our same-day appointment system ensures you receive prompt, personalized treatment from providers who know your medical history.',
      services: [
        'Flu and cold symptoms with rapid diagnosis',
        'Minor injuries and cuts requiring immediate assessment',
        'Fever and infection treatment with antibiotic therapy',
        'Digestive issues including nausea and abdominal pain',
        'Respiratory symptoms and chest discomfort',
        'Skin conditions requiring immediate evaluation'
      ],
      process: [
        'Morning appointment availability for urgent needs',
        'Walk-in hours Monday-Friday 8:00-10:00 AM & 2:00-4:00 PM',
        'Rapid assessment protocols for quick diagnosis',
        'On-site treatment capabilities and medications',
        'Electronic prescribing to local pharmacies',
        'Follow-up care coordination for continuity'
      ],
      relatedServices: ['Family Medicine & Primary Care', 'Telemedicine Services', 'Chronic Care Management'],
      faqs: [
        {
          question: 'How quickly can I be seen for same-day care?',
          answer: 'We reserve appointment slots each morning for same-day needs. Call first thing in the morning for best availability, and we prioritize urgent medical conditions.'
        },
        {
          question: 'What conditions qualify for same-day appointments?',
          answer: 'Any urgent medical condition that cannot wait for routine scheduling, including flu symptoms, minor injuries, infections, fever, and acute pain or discomfort.'
        }
      ]
    }
  },
  'telemedicine': {
    title: 'Telemedicine & Virtual Consultations',
    seoTitle: 'Telemedicine Services | Kirkwood Medical Practice',
    description: 'Convenient virtual healthcare visits that eliminate travel time while providing secure medical care.',
    icon: FaLaptopMedical,
    color: 'healing-teal',
    content: {
      overview: 'Modern healthcare delivery that brings medical expertise directly to your home, our telemedicine services provide convenient, secure, and effective medical care for a wide range of health needs. Experience the convenience of virtual healthcare without sacrificing the personal relationships that define quality primary care.',
      services: [
        'Follow-up consultations for ongoing health conditions',
        'Chronic condition monitoring and management',
        'Prescription refill consultations and reviews',
        'Mental health check-ins and wellness monitoring',
        'Preventive care discussions and planning',
        'Lab result reviews with detailed explanations'
      ],
      process: [
        'Secure video platform setup with user-friendly technology',
        'Pre-visit health questionnaire completion',
        'Real-time consultation with your provider',
        'Digital prescription processing to your pharmacy',
        'Electronic health record integration',
        'Follow-up appointment scheduling as needed'
      ],
      relatedServices: ['Chronic Care Management', 'Family Medicine', 'Same-Day Care'],
      faqs: [
        {
          question: 'What technology do I need for telemedicine visits?',
          answer: 'You need a device with a camera and internet connection - smartphone, tablet, or computer. Our platform is easy to use and requires no special software downloads.'
        },
        {
          question: 'Are telemedicine visits covered by insurance?',
          answer: 'Most insurance plans cover telehealth services at the same rate as in-office visits. We\'ll verify your coverage and handle insurance billing just like traditional appointments.'
        }
      ]
    }
  },
  'womens-health': {
    title: 'Women\'s Health Services',
    seoTitle: 'Women\'s Health Services | Kirkwood Medical Practice', 
    description: 'Specialized healthcare designed for women\'s unique health needs throughout all life stages.',
    icon: FaHeartbeat,
    color: 'care-red',
    content: {
      overview: 'Specialized healthcare designed specifically for women\'s unique health needs, our women\'s health services provide compassionate, comprehensive care throughout all life stages. From reproductive health to menopause management, we deliver personalized care that respects your individual health journey.',
      services: [
        'Annual gynecological examinations and screenings',
        'Family planning consultations and contraception counseling',
        'Prenatal care coordination with obstetric referrals',
        'Contraception management and hormone therapy options',
        'Menopause support and hormone replacement therapy',
        'Reproductive health screenings and STD testing'
      ],
      process: [
        'Compassionate, confidential care in comfortable environment',
        'Evidence-based treatment using latest medical research',
        'Personalized health planning for individual goals',
        'Preventive screening schedules optimized for your needs',
        'Specialist referral coordination when needed',
        'Patient education emphasis for informed decisions'
      ],
      relatedServices: ['Annual & School Physicals', 'Family Medicine', 'Chronic Care Management'],
      faqs: [
        {
          question: 'What women\'s health services do you provide?',
          answer: 'We provide comprehensive women\'s healthcare including annual exams, family planning, prenatal coordination, contraception management, menopause support, and reproductive health screenings.'
        },
        {
          question: 'Do you provide obstetric care?',
          answer: 'We provide prenatal care coordination and work closely with obstetric specialists. Dr. Abdallah specializes in women\'s health including obstetrics and can manage early pregnancy care and referrals.'
        }
      ]
    }
  },
  'chronic-care': {
    title: 'Chronic Condition Management',
    seoTitle: 'Chronic Condition Management | Kirkwood Medical Practice',
    description: 'Expert ongoing care for chronic conditions that improves quality of life and prevents complications.',
    icon: FaVials,
    color: 'healing-teal',
    content: {
      overview: 'Specialized ongoing care for chronic conditions that improves quality of life and prevents complications, our chronic disease management program provides comprehensive support for patients living with diabetes, hypertension, heart disease, and other long-term health conditions. We partner with you to optimize your health while managing complex medical needs.',
      services: [
        'Diabetes monitoring and treatment with complication prevention',
        'Hypertension management with medication optimization',
        'Heart disease care coordination with specialists',
        'Arthritis pain management with multiple treatment approaches',
        'Asthma and respiratory conditions with inhaler training',
        'Thyroid disorder treatment with hormone monitoring'
      ],
      process: [
        'Regular monitoring appointments with consistent providers',
        'Medication optimization for maximum benefit',
        'Lifestyle modification counseling and support',
        'Specialist coordination and communication',
        'Emergency care planning for acute complications',
        'Patient education programs for active participation'
      ],
      relatedServices: ['Family Medicine', 'Telemedicine Services', 'Prescription Management'],
      faqs: [
        {
          question: 'How often will I need appointments for chronic care?',
          answer: 'Appointment frequency depends on your specific conditions and stability. Typically, patients with well-controlled conditions are seen every 3-6 months, while those requiring closer monitoring may need monthly visits.'
        },
        {
          question: 'Do you coordinate with my specialists?',
          answer: 'Yes, we maintain communication with your specialists and coordinate all aspects of your care. We serve as your primary care hub, ensuring all providers are informed about your complete health picture.'
        }
      ]
    }
  },
  'physical-exams': {
    title: 'Annual & School Physicals',
    seoTitle: 'Annual & School Physicals | Kirkwood Medical Practice',
    description: 'Thorough health assessments for wellness maintenance, school requirements, and employment needs.',
    icon: FaUserMd,
    color: 'medical-blue',
    content: {
      overview: 'Thorough health assessments that form the foundation of preventive care, our physical examination services provide comprehensive evaluations for wellness maintenance, school requirements, sports participation, and employment needs. We combine detailed health assessments with personalized care planning to optimize your long-term health outcomes.',
      services: [
        'Annual wellness examinations with complete health assessments',
        'School enrollment physicals with efficient documentation',
        'Sports participation physicals with safety evaluations',
        'Pre-employment health screenings and fitness assessments',
        'DOT physical examinations for commercial drivers',
        'Immigration medical exams meeting USCIS requirements'
      ],
      process: [
        'Complete health history review including family history',
        'Thorough physical assessment of all body systems',
        'Age-appropriate screening tests and diagnostic studies',
        'Vaccination status evaluation with updates',
        'Health risk assessment and preventive opportunities',
        'Documentation completion with prompt form processing'
      ],
      relatedServices: ['Family Medicine', 'Women\'s Health Services', 'Chronic Care Management'],
      faqs: [
        {
          question: 'How long does a complete physical exam take?',
          answer: 'Annual physical exams typically take 45-60 minutes to ensure thorough evaluation. School and sports physicals are usually completed in 30-45 minutes with quick form processing.'
        },
        {
          question: 'What should I bring to my physical exam?',
          answer: 'Bring your insurance card, ID, list of current medications, any required forms, and your vaccination record if available. We can also access your medical history if you\'ve been our patient before.'
        }
      ]
    }
  }
};

export async function generateMetadata({ params }: ServicePageProps): Promise<Metadata> {
  const { slug } = await params;
  const service = servicesData[slug as keyof typeof servicesData];
  
  if (!service) {
    return {
      title: 'Service Not Found | Kirkwood Medical Practice'
    };
  }

  return {
    title: service.seoTitle,
    description: service.description,
  };
}

export default async function ServiceDetailPage({ params }: ServicePageProps) {
  const { slug } = await params;
  const service = servicesData[slug as keyof typeof servicesData];

  if (!service) {
    notFound();
  }

  const IconComponent = service.icon;
  const colorClasses = {
    'medical-blue': {
      bg: 'bg-medical-blue',
      text: 'text-medical-blue',
      border: 'border-medical-blue',
      bgOpacity: 'bg-medical-blue bg-opacity-10'
    },
    'care-red': {
      bg: 'bg-care-red',
      text: 'text-care-red', 
      border: 'border-care-red',
      bgOpacity: 'bg-care-red bg-opacity-10'
    },
    'healing-teal': {
      bg: 'bg-healing-teal',
      text: 'text-healing-teal',
      border: 'border-healing-teal',
      bgOpacity: 'bg-healing-teal bg-opacity-10'
    }
  };

  const colors = colorClasses[service.color as keyof typeof colorClasses];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className={`bg-gradient-to-br from-medical-blue to-trust-navy text-white py-16`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            <div className="flex justify-center mb-6">
              <div className="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <IconComponent className="w-10 h-10 text-white" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold font-source-sans mb-6">
              {service.title}
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              {service.content.overview}
            </p>
          </div>
        </div>
      </section>

      {/* Services Details */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12">
            <div>
              <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-6">
                Services We Provide
              </h2>
              <div className="space-y-4">
                {service.content.services.map((serviceItem, index) => (
                  <div key={index} className="flex items-start">
                    <FaCheckCircle className={`w-5 h-5 ${colors.text} mt-1 mr-3 flex-shrink-0`} />
                    <p className="text-comfort-gray">{serviceItem}</p>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-6">
                Our Process
              </h2>
              <div className="space-y-4">
                {service.content.process.map((step, index) => (
                  <div key={index} className="flex items-start">
                    <div className={`w-8 h-8 ${colors.bg} text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0`}>
                      {index + 1}
                    </div>
                    <p className="text-comfort-gray pt-1">{step}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Service Areas */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-source-sans text-trust-navy mb-4">
              Available Throughout Broome County
            </h2>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 mb-8">
            <div className="text-center">
              <FaMapMarkerAlt className={`w-8 h-8 ${colors.text} mx-auto mb-3`} />
              <h3 className="text-xl font-semibold text-trust-navy mb-2">Kirkwood</h3>
              <p className="text-comfort-gray">Comprehensive services at our home base</p>
            </div>
            <div className="text-center">
              <FaMapMarkerAlt className={`w-8 h-8 ${colors.text} mx-auto mb-3`} />
              <h3 className="text-xl font-semibold text-trust-navy mb-2">Binghamton</h3>
              <p className="text-comfort-gray">Full-service care for urban families</p>
            </div>
            <div className="text-center">
              <FaMapMarkerAlt className={`w-8 h-8 ${colors.text} mx-auto mb-3`} />
              <h3 className="text-xl font-semibold text-trust-navy mb-2">Conklin</h3>
              <p className="text-comfort-gray">Accessible rural healthcare options</p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQs */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold font-source-sans text-trust-navy text-center mb-12">
            Frequently Asked Questions
          </h2>
          <div className="space-y-8">
            {service.content.faqs.map((faq, index) => (
              <div key={index} className={`border-l-4 ${colors.border} pl-6 py-4`}>
                <h3 className="text-xl font-semibold text-trust-navy mb-3">
                  {faq.question}
                </h3>
                <p className="text-comfort-gray leading-relaxed">
                  {faq.answer}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Related Services */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold font-source-sans text-trust-navy text-center mb-12">
            Related Services
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {service.content.relatedServices.map((relatedService, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-md text-center">
                <h3 className="text-lg font-semibold text-trust-navy mb-2">
                  {relatedService}
                </h3>
                <Link href="/services" className={`${colors.text} hover:opacity-80 font-medium`}>
                  Learn More →
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-medical-blue to-trust-navy text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold font-source-sans mb-6">
            Experience {service.title}
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Schedule your appointment today and experience the personalized care that sets us apart from large health systems.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <CTA href="/contact" variant="primary" size="lg">
              Schedule Your Appointment
            </CTA>
            <CTA href="tel:**********" variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-medical-blue">
              <FaPhone className="mr-2" />
              Call (*************
            </CTA>
          </div>
        </div>
      </section>
    </div>
  );
}