import type { Metadata, Viewport } from 'next';
import './globals.css';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
}

export const metadata: Metadata = {
  metadataBase: new URL('https://kirkwoodmedical.com'),
  title: {
    template: '%s | Kirkwood Medical Practice',
    default: 'Kirkwood Medical Practice - Primary Care - Broome County, New York'
  },
  description: 'Comprehensive primary care services in Kirkwood, Binghamton, and Conklin. Same-day appointments, telemedicine, women\'s health, and chronic care management. New patients welcome.',
  keywords: 'primary care, family medicine, same-day appointments, telemedicine, womens health, chronic care, Kirkwood NY, Binghamton NY, Conklin NY, Broome County',
  authors: [{ name: 'Kirkwood Medical Practice' }],
  creator: 'Kirkwood Medical Practice',
  publisher: 'Kirkwood Medical Practice',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    title: 'Kirkwood Medical Practice - Primary Care - Broome County, New York',
    description: 'Comprehensive primary care services in Kirkwood, Binghamton, and Conklin. Same-day appointments, telemedicine, women\'s health, and chronic care management.',
    type: 'website',
    locale: 'en_US',
    url: 'https://kirkwoodmedical.com',
    siteName: 'Kirkwood Medical Practice',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Kirkwood Medical Practice - Primary Care - Broome County, New York',
    description: 'Comprehensive primary care services in Kirkwood, Binghamton, and Conklin.',
    creator: '@KirkwoodMedical',
  },
  robots: {
    index: true,
    follow: true,
    nocache: true,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
  manifest: '/site.webmanifest',
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "MedicalOrganization",
    "name": "Kirkwood Medical Practice",
    "description": "Comprehensive primary care services in Kirkwood, Binghamton, and Conklin. Same-day appointments, telemedicine, women's health, and chronic care management.",
    "url": "https://kirkwoodmedical.com",
    "logo": "https://kirkwoodmedical.com/logo.png",
    "image": "https://kirkwoodmedical.com/practice-photo.jpg",
    "telephone": "+16077297464",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "4290 Vestal Road",
      "addressLocality": "Vestal",
      "addressRegion": "NY",
      "postalCode": "13850",
      "addressCountry": "US"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "42.0834",
      "longitude": "-76.0560"
    },
    "openingHours": "Mo-Fr 08:00-17:00",
    "areaServed": [
      {
        "@type": "City",
        "name": "Kirkwood",
        "addressRegion": "NY"
      },
      {
        "@type": "City", 
        "name": "Binghamton",
        "addressRegion": "NY"
      },
      {
        "@type": "City",
        "name": "Conklin", 
        "addressRegion": "NY"
      }
    ],
    "medicalSpecialty": [
      "Family Medicine",
      "Primary Care",
      "Women's Health",
      "Chronic Disease Management"
    ],
    "serviceType": [
      "Primary Care",
      "Same-day appointments",
      "Telemedicine",
      "Physical examinations",
      "Chronic care management"
    ]
  };

  return (
    <html lang="en">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      </head>
      <body className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}