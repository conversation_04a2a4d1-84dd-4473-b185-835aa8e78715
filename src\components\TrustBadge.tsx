import { ReactNode } from 'react';
import { 
  FaUserMd, 
  FaHospital, 
  FaCertificate, 
  FaAward,
  FaShieldAlt,
  FaHeart,
  FaCalendarCheck,
  FaPhone
} from 'react-icons/fa';

interface TrustBadgeProps {
  type: 'certification' | 'experience' | 'insurance' | 'sameday' | 'community' | 'quality' | 'telehealth' | 'contact';
  title: string;
  description: string;
  className?: string;
}

const iconMap = {
  certification: FaCertificate,
  experience: FaUserMd,
  insurance: FaShieldAlt,
  sameday: FaCalendarCheck,
  community: FaHeart,
  quality: FaAward,
  telehealth: FaHospital,
  contact: FaPhone
};

export default function TrustBadge({ type, title, description, className = '' }: TrustBadgeProps) {
  const Icon = iconMap[type];

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 border-l-4 border-medical-blue ${className}`}>
      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          <div className="w-12 h-12 bg-medical-blue bg-opacity-10 rounded-full flex items-center justify-center">
            <Icon className="w-6 h-6 text-medical-blue" />
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-trust-navy mb-2">
            {title}
          </h3>
          <p className="text-comfort-gray text-sm leading-relaxed">
            {description}
          </p>
        </div>
      </div>
    </div>
  );
}