@import 'tailwindcss';
@import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

@layer base {
  :root {
    /* Custom color variables for Tailwind */
    --color-medical-blue: #3351AB;
    --color-care-red: #EC1D26;
    --color-trust-navy: #1E2B5C;
    --color-healing-teal: #4A9B9B;
    --color-comfort-gray: #6B7280;
    
    /* Font family variables */
    --font-source-sans: 'Source Sans Pro', sans-serif;
    --font-open-sans: 'Open Sans', sans-serif;
  }

  body {
    font-family: var(--font-open-sans);
    color: var(--color-trust-navy);
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-source-sans);
    color: var(--color-trust-navy);
  }
}

@layer utilities {
  /* Custom color utilities */
  .text-medical-blue { color: var(--color-medical-blue); }
  .text-care-red { color: var(--color-care-red); }
  .text-trust-navy { color: var(--color-trust-navy); }
  .text-healing-teal { color: var(--color-healing-teal); }
  .text-comfort-gray { color: var(--color-comfort-gray); }
  
  .bg-medical-blue { background-color: var(--color-medical-blue); }
  .bg-care-red { background-color: var(--color-care-red); }
  .bg-trust-navy { background-color: var(--color-trust-navy); }
  .bg-healing-teal { background-color: var(--color-healing-teal); }
  .bg-comfort-gray { background-color: var(--color-comfort-gray); }
  
  .border-medical-blue { border-color: var(--color-medical-blue); }
  .border-care-red { border-color: var(--color-care-red); }
  .border-trust-navy { border-color: var(--color-trust-navy); }
  .border-healing-teal { border-color: var(--color-healing-teal); }
  .border-comfort-gray { border-color: var(--color-comfort-gray); }
  
  .hover\:text-medical-blue:hover { color: var(--color-medical-blue); }
  .hover\:text-care-red:hover { color: var(--color-care-red); }
  .hover\:text-trust-navy:hover { color: var(--color-trust-navy); }
  .hover\:text-healing-teal:hover { color: var(--color-healing-teal); }
  .hover\:text-comfort-gray:hover { color: var(--color-comfort-gray); }
  
  .hover\:bg-medical-blue:hover { background-color: var(--color-medical-blue); }
  .hover\:bg-care-red:hover { background-color: var(--color-care-red); }
  .hover\:bg-trust-navy:hover { background-color: var(--color-trust-navy); }
  .hover\:bg-healing-teal:hover { background-color: var(--color-healing-teal); }
  .hover\:bg-comfort-gray:hover { background-color: var(--color-comfort-gray); }
  
  .hover\:border-medical-blue:hover { border-color: var(--color-medical-blue); }
  .hover\:border-care-red:hover { border-color: var(--color-care-red); }
  .hover\:border-trust-navy:hover { border-color: var(--color-trust-navy); }
  .hover\:border-healing-teal:hover { border-color: var(--color-healing-teal); }
  .hover\:border-comfort-gray:hover { border-color: var(--color-comfort-gray); }
  
  /* Font family utilities */
  .font-source-sans { font-family: var(--font-source-sans); }
  .font-open-sans { font-family: var(--font-open-sans); }
}