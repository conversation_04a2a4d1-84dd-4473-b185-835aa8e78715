import Link from 'next/link';
import { FaPhone, FaMapMarkerAlt, FaEnvelope, FaClock } from 'react-icons/fa';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  const services = [
    { name: 'Family Medicine', href: '/services/family-medicine' },
    { name: 'Same-Day Care', href: '/services/same-day-care' },
    { name: 'Telemedicine', href: '/services/telemedicine' },
    { name: 'Women\'s Health', href: '/services/womens-health' },
    { name: 'Chronic Care', href: '/services/chronic-care' },
    { name: 'Physical Exams', href: '/services/physical-exams' }
  ];

  const locations = [
    { name: 'Kirkwood', href: '/locations/kirkwood' },
    { name: 'Binghamton', href: '/locations/binghamton' },
    { name: 'Conklin', href: '/locations/conklin' }
  ];

  const quickLinks = [
    { name: 'About Us', href: '/about' },
    { name: 'Our Team', href: '/about#team' },
    { name: 'Contact Us', href: '/contact' },
    { name: 'Patient Portal', href: 'https://kirkwoodmedicalpatientportal.com', external: true },
    { name: 'Schedule Appointment', href: 'https://kirkwoodmedicalscheduling.com', external: true }
  ];

  return (
    <footer className="bg-trust-navy text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Practice Info */}
          <div className="space-y-4">
            <div className="text-2xl font-bold font-source-sans text-white">
              Kirkwood Medical Practice
            </div>
            <p className="text-gray-300 text-sm">
              Comprehensive primary care services for families throughout Broome County, New York.
            </p>
            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-300">
                <FaMapMarkerAlt className="mr-2 flex-shrink-0" />
                <span>4290 Vestal Road, Vestal, NY 13850</span>
              </div>
              <div className="flex items-center text-sm text-gray-300">
                <FaPhone className="mr-2 flex-shrink-0" />
                <a href="tel:**********" className="hover:text-white transition-colors">
                  (*************
                </a>
              </div>
              <div className="flex items-center text-sm text-gray-300">
                <FaEnvelope className="mr-2 flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold font-source-sans mb-4">Our Services</h3>
            <ul className="space-y-2">
              {services.map((service) => (
                <li key={service.name}>
                  <Link
                    href={service.href}
                    className="text-gray-300 hover:text-white text-sm transition-colors"
                  >
                    {service.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Locations */}
          <div>
            <h3 className="text-lg font-semibold font-source-sans mb-4">Service Areas</h3>
            <ul className="space-y-2">
              {locations.map((location) => (
                <li key={location.name}>
                  <Link
                    href={location.href}
                    className="text-gray-300 hover:text-white text-sm transition-colors"
                  >
                    {location.name}
                  </Link>
                </li>
              ))}
            </ul>
            <div className="mt-6">
              <h4 className="font-semibold mb-2 text-sm">Office Hours</h4>
              <div className="text-sm text-gray-300 space-y-1">
                <div className="flex items-center">
                  <FaClock className="mr-2 flex-shrink-0" />
                  <span>Mon-Fri: 8:00 AM - 5:00 PM</span>
                </div>
                <div className="ml-6 text-xs">
                  Walk-in hours: 8:00-10:00 AM & 2:00-4:00 PM
                </div>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold font-source-sans mb-4">Quick Links</h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  {link.external ? (
                    <a
                      href={link.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-300 hover:text-white text-sm transition-colors"
                    >
                      {link.name}
                    </a>
                  ) : (
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-white text-sm transition-colors"
                    >
                      {link.name}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-300 text-sm">
              © {currentYear} Kirkwood Medical Practice. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="/privacy" className="text-gray-300 hover:text-white text-sm transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-gray-300 hover:text-white text-sm transition-colors">
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}