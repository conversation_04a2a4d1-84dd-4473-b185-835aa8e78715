import Link from 'next/link';
import { ReactNode } from 'react';

interface CTAProps {
  href?: string;
  onClick?: () => void;
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  external?: boolean;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
}

export default function CTA({ 
  href, 
  onClick, 
  children, 
  variant = 'primary', 
  size = 'md',
  className = '',
  external = false,
  type = 'button',
  disabled = false
}: CTAProps) {
  const baseClasses = "inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-opacity-50 min-h-[44px] min-w-[44px]";
  
  const variants = {
    primary: "bg-care-red text-white hover:bg-red-700 focus:ring-care-red",
    secondary: "bg-medical-blue text-white hover:bg-blue-800 focus:ring-medical-blue",
    outline: "border-2 border-medical-blue text-medical-blue hover:bg-medical-blue hover:text-white focus:ring-medical-blue"
  };

  const sizes = {
    sm: "px-4 py-2 text-sm",
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg"
  };

  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`;

  if (href) {
    if (external) {
      return (
        <a
          href={href}
          className={classes}
          target="_blank"
          rel="noopener noreferrer"
        >
          {children}
        </a>
      );
    }
    
    return (
      <Link href={href} className={classes}>
        {children}
      </Link>
    );
  }

  return (
    <button onClick={onClick} type={type} disabled={disabled} className={`${classes} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
      {children}
    </button>
  );
}