{"permissions": {"allow": ["Bash(npm init:*)", "Bash(npm install:*)", "<PERSON><PERSON>(npx playwright:*)", "Bash(claude mcp add:*)", "Bash(npx create-next-app:*)", "Bash(rm:*)", "Bash(npx tailwindcss init:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run dev:*)", "Bash(npm run build:*)", "Bash(npx:*)", "<PERSON><PERSON>(touch:*)", "Bash(cp:*)", "Bash(node_modules.bintailwindcss -i ./src/styles/input.css -o ./src/app/globals.css --content \"./src/**/*.{js,ts,jsx,tsx}\")", "<PERSON><PERSON>(timeout:*)"], "deny": [], "ask": []}}